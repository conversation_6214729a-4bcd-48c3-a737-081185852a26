<script setup lang="ts">
const { footer } = useAppConfig()
const { t } = useI18n()

// Compute translated footer links
const translatedFooterLinks = computed(() => {
  if (!footer?.links) return []

  return footer.links.map((link) => {
    const translatedLink = { ...link }

    // Translate labels based on the key
    if (link.label === 'Privacy') {
      translatedLink.label = t('footer.privacy')
      translatedLink['aria-label'] = t('footer.privacyPolicy')
    } else if (link.label === 'Terms') {
      translatedLink.label = t('footer.terms')
      translatedLink['aria-label'] = t('footer.termsOfService')
    } else if (link['aria-label']) {
      // Translate aria-labels for icon-only links
      if (link['aria-label'] === 'Text To Speech OpenAI') {
        translatedLink['aria-label'] = t('footer.textToSpeechOpenAI')
      } else if (link['aria-label'] === 'DoctransGPT') {
        translatedLink['aria-label'] = t('footer.doctransGPT')
      } else if (link['aria-label'] === 'Nuxt UI on Discord') {
        translatedLink['aria-label'] = t('footer.nuxtUIOnDiscord')
      } else if (link['aria-label'] === 'Youtube') {
        translatedLink['aria-label'] = t('footer.youtube')
      }
    }

    return translatedLink
  })
})
</script>

<template>
  <UFooter
    class="z-10 bg-default"
    :ui="{ left: 'text-xs' }"
  >
    <template #left>
      <div class="flex flex-col gap-1 text-xs">
        <div>
          {{ $t("copyright", { year: new Date().getFullYear() }) }}
        </div>
        <div class="text-gray-500 dark:text-gray-400">
          {{ $t("footer.supportEmail") }}: <EMAIL>
        </div>
        <div class="text-gray-500 dark:text-gray-400">
          {{ $t("footer.address") }}: {{ $t("footer.companyAddress") }}
        </div>
      </div>
    </template>

    <template #right>
      <div class="flex gap-1 items-center">
        <template v-if="translatedFooterLinks.length">
          <UButton
            v-for="(link, index) of translatedFooterLinks"
            :key="index"
            v-bind="{
              size: 'xs',
              color: 'neutral',
              variant: 'ghost',
              ...link
            }"
          />
        </template>
      </div>
    </template>
  </UFooter>
</template>
