<script setup lang="ts">
import { en, vi, ja, zh_cn, es, fr, de, pt } from '@nuxt/ui/locale'
import { storeToRefs } from 'pinia'
import { watch } from 'vue'

const nuxtApp = useNuxtApp()
const { activeHeadings, updateHeadings } = useScrollspy()
const { t } = useI18n()
const appStore = useAppStore()
const { lastMenus, loading, locale, localeForI18n } = storeToRefs(appStore)
const router = useRouter()
const route = useRoute()

// Language and locale handling
const { locale: i18nLocale, setLocale } = useI18n()
watch(i18nLocale, (newLocale: string) => {
  // zh-CN -> zh_cn
  locale.value = newLocale
  setLocale(localeForI18n.value)
})
const items = computed(() => [
  {
    label: t('Home'),
    onClick: () => {
      router.push({
        name: lastMenus.value.app
      })
    },
    active: route.name?.toString().includes('app'),
    showsOn: ['header', 'drawer']
  },
  {
    label: t('History'),
    to: '/history',
    showsOn: ['header', 'drawer']
  },
  {
    label: t('Pricing'),
    to: '/pricing',
    showsOn: ['header', 'drawer']
  },
  {
    label: t('API'),
    to: '/api-docs',
    showsOn: ['header', 'drawer']
  },
  {
    label: t('footer.privacy'),
    to: '/privacy',
    showsOn: ['drawer']
  },
  {
    label: t('footer.terms'),
    to: '/terms',
    showsOn: ['drawer']
  }
])

nuxtApp.hooks.hookOnce('page:finish', () => {
  updateHeadings(
    [
      document.querySelector('#features'),
      document.querySelector('#pricing'),
      document.querySelector('#testimonials')
    ].filter(Boolean) as Element[]
  )
})

const authStore = useAuthStore()
const { user, user_credit } = storeToRefs(authStore)
</script>

<template>
  <UHeader
    toggle-side="left"
    mode="drawer"
    :ui="{
      toggle: 'lg:flex mr-2',
      content: 'lg:block',
      overlay: 'lg:block'
    }"
    :menu="{
      direction: 'left',
      handle: false,
      shouldScaleBackground: false,
      setBackgroundColorOnScale: false
    }"
  >
    <template #left>
      <NuxtLink to="/">
        <div class="flex flex-row gap-4 items-center mr-4">
          <BaseLargeLogo
            id="main-logo"
            :loading="loading"
            :size="`small`"
          />
          <BaseAppTitle
            class="justify-center text-center flex mx-auto !text-xl"
          />
        </div>
      </NuxtLink>
      <UNavigationMenu
        :items="items.filter((item) => item.showsOn.includes('header'))"
        variant="link"
        class="hidden lg:block"
      />
    </template>

    <template #right>
      <div class="flex flex-row gap-2 items-center">
        <div
          v-if="user"
          class="flex flex-row gap-2 items-center"
        >
          <!-- <UButton
            color="primary"
            variant="soft"
            trailing-icon="ic:baseline-plus"
            size="sm"
            class="hidden sm:block"
          >
            <div>
              {{ formatNumber(user_credit?.available_credit || 0) }}
              {{ $t("Credits") }}
            </div>
          </UButton> -->

          <!-- Language and Dark Mode Selectors -->
          <ULocaleSelect
            v-model="i18nLocale"
            :locales="[en, ja, vi, zh_cn, es, fr, de, pt]"
            variant="ghost"
            class="hidden sm:block"
            :ui="{
              content: 'w-40'
            }"
          />
          <BuyCreditsButton class="hidden sm:flex" />

          <ClientOnly>
            <UColorModeButton />
          </ClientOnly>

          <NotificationBell />
          <AppUserMenu />
        </div>
        <div
          v-else
          class="flex flex-row gap-2 items-center"
        >
          <ULocaleSelect
            v-model="i18nLocale"
            :locales="[en, ja, vi, zh_cn, es, fr, de, pt]"
            variant="ghost"
            class="hidden sm:block"
            :ui="{
              content: 'w-40'
            }"
          />

          <ClientOnly>
            <UColorModeButton />
          </ClientOnly>

          <UButton
            :label="$t('Login')"
            variant="subtle"
            color="neutral"
            class="hidden lg:block"
            to="/auth/login"
          />
          <UButton
            :label="$t('ui.buttons.signUp')"
            variant="solid"
            class="hidden lg:block"
            to="/auth/signup"
          />
        </div>
      </div>
    </template>

    <template #body>
      <div class="flex flex-col justify-between h-full">
        <div>
          <NuxtLink to="/">
            <div class="flex flex-row gap-4 items-center mb-8">
              <BaseLargeLogo
                id="main-logo"
                :loading="loading"
                :size="`small`"
              />
              <BaseAppTitle
                class="justify-center text-center flex mx-auto !text-xl"
              />
            </div>
          </NuxtLink>
          <UNavigationMenu
            :items="items.filter((item) => item.showsOn.includes('drawer'))"
            orientation="vertical"
            class="-mx-2.5"
          />
        </div>
        <div class="flex flex-col gap-3 mt-10">
          <BuyCreditsButton
            v-if="user"
            class="min-w-[200px]"
          />
          <div
            v-else
            class="flex flex-col gap-3"
          >
            <UButton
              :label="$t('Login')"
              variant="subtle"
              color="neutral"
              to="/auth/login"
            />
            <UButton
              :label="$t('ui.buttons.signUp')"
              variant="solid"
              to="/auth/signup"
            />
          </div>
          <ULocaleSelect
            v-model="i18nLocale"
            :locales="[en, ja, vi, zh_cn, es, fr, de, pt]"
            class="w-full"
            size="sm"
            :ui="{}"
          />
          <UColorModeSelect
            size="sm"
            class="w-full"
          />
        </div>
      </div>
    </template>
  </UHeader>
</template>
